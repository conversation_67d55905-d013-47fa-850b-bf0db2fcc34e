﻿<Window x:Class="SmartInvoiceManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SmartInvoiceManager"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Smart Invoice Manager - نظام إدارة الفواتير الذكي"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0" Background="{DynamicResource MaterialDesignDarkBackground}">
            <StackPanel>
                <!-- Logo/Header -->
                <Border Background="{DynamicResource PrimaryHueMidBrush}" Height="80">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Receipt"
                                               Foreground="White"
                                               Width="32" Height="32"/>
                        <TextBlock Text="Smart Invoice"
                                 Foreground="White"
                                 FontWeight="Bold"
                                 FontSize="16"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="Manager"
                                 Foreground="White"
                                 FontWeight="Bold"
                                 FontSize="16"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <StackPanel Margin="0,20,0,0">
                    <Button x:Name="DashboardButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="DashboardButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="ViewDashboard"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="لوحة التحكم"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="InvoicesButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="InvoicesButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="Receipt"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="الفواتير"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CustomersButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="CustomersButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="AccountGroup"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="العملاء"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ProductsButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="ProductsButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="Package"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="المنتجات"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ReportsButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="ReportsButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="ChartLine"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="التقارير"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SettingsButton"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Height="50"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Click="SettingsButton_Click">
                        <StackPanel Orientation="Horizontal" Margin="20,0">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="الإعدادات"
                                     Margin="15,0,0,0"
                                     VerticalAlignment="Center"
                                     FontSize="14"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0" Background="White"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,0,0,1">
                <Grid Margin="20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="PageTitleTextBlock"
                             Text="لوحة التحكم"
                             FontSize="24"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBody}"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="الإشعارات">
                            <materialDesign:PackIcon Kind="Bell"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="الملف الشخصي">
                            <materialDesign:PackIcon Kind="Account"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Frame -->
            <Frame x:Name="MainContentFrame"
                   Grid.Row="1"
                   NavigationUIVisibility="Hidden"
                   Background="{DynamicResource MaterialDesignPaper}"/>
        </Grid>
    </Grid>
</Window>
