using System.Windows.Controls;

namespace SmartInvoiceManager.Views;

/// <summary>
/// Interaction logic for ReportsView.xaml
/// </summary>
public partial class ReportsView : Page
{
    public ReportsView()
    {
        InitializeComponent();
        LoadSampleReportData();
    }

    private void LoadSampleReportData()
    {
        var sampleData = new[]
        {
            new { Month = "يناير 2024", InvoiceCount = 8, TotalSales = 45000.00m, TotalTax = 6750.00m, NetProfit = 38250.00m },
            new { Month = "فبراير 2024", InvoiceCount = 12, TotalSales = 62000.00m, TotalTax = 9300.00m, NetProfit = 52700.00m },
            new { Month = "مارس 2024", InvoiceCount = 5, TotalSales = 18000.00m, TotalTax = 2700.00m, NetProfit = 15300.00m }
        };

        ReportsDataGrid.ItemsSource = sampleData;
    }
}
