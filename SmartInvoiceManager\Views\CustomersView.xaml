<Page x:Class="SmartInvoiceManager.Views.CustomersView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="CustomersView">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="إدارة العملاء" 
                     FontSize="24" 
                     FontWeight="Bold"
                     VerticalAlignment="Center"/>

            <Button Grid.Column="1" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="AccountPlus" 
                                           Width="20" Height="20" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="عميل جديد" 
                             Margin="5,0,0,0" 
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- Search -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" 
                         materialDesign:HintAssist.Hint="البحث في العملاء..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,10,0"/>

                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignIconButton}"
                        ToolTip="بحث">
                    <materialDesign:PackIcon Kind="Magnify"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Customers List -->
        <materialDesign:Card Grid.Row="2">
            <DataGrid AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="اسم العميل" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                    <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="250"/>
                    <DataGridTextColumn Header="الرقم الضريبي" Binding="{Binding TaxNumber}" Width="150"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل">
                                        <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Foreground="Red">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
