# Smart Invoice Manager - نظام إدارة الفواتير الذكي

نظام شامل لإدارة الفواتير والعملاء مبني بتقنية WPF و .NET 8 مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏠 لوحة التحكم
- عرض إحصائيات شاملة للفواتير والعملاء
- مؤشرات الأداء الرئيسية (KPIs)
- إجراءات سريعة للمهام الشائعة

### 📄 إدارة الفواتير
- إنشاء وتعديل الفواتير بسهولة
- حساب تلقائي للضرائب والإجماليات
- دعم حالات متعددة للفواتير (مسودة، مرسلة، مدفوعة، متأخرة، ملغاة)
- ترقيم تلقائي للفواتير
- إضافة منتجات وخدمات متعددة لكل فاتورة

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تخزين معلومات الاتصال والعناوين
- ربط العملاء بفواتيرهم
- بحث متقدم في قاعدة بيانات العملاء

### 📦 إدارة المنتجات والخدمات
- كتالوج شامل للمنتجات والخدمات
- إدارة الأسعار والوحدات
- تتبع المخزون
- تصنيف المنتجات

### 📊 التقارير والإحصائيات
- تقارير شهرية وسنوية
- إحصائيات المبيعات والأرباح
- تقارير العملاء والمنتجات
- تصدير التقارير إلى Excel

### ⚙️ الإعدادات
- إعدادات معلومات الشركة
- رفع شعار الشركة
- إعدادات الضرائب والعملة
- نسخ احتياطية تلقائية
- تحديثات تلقائية

## التقنيات المستخدمة

### Frontend
- **WPF (Windows Presentation Foundation)** - واجهة المستخدم
- **Material Design in XAML** - تصميم حديث وجذاب
- **MVVM Pattern** - فصل المنطق عن الواجهة

### Backend
- **.NET 8** - الإطار الأساسي
- **Entity Framework Core** - طبقة الوصول للبيانات
- **SQLite** - قاعدة البيانات المحلية
- **Dependency Injection** - حقن التبعيات

### Libraries & Packages
- **CommunityToolkit.Mvvm** - تطبيق نمط MVVM
- **ClosedXML** - تصدير ملفات Excel
- **Squirrel.Windows** - التحديث التلقائي
- **xUnit** - اختبارات الوحدة

## هيكل المشروع

```
SmartInvoiceManager/
├── SmartInvoiceManager/              # المشروع الرئيسي (WPF)
│   ├── Views/                        # واجهات المستخدم
│   ├── Converters/                   # محولات البيانات
│   └── App.xaml                      # إعدادات التطبيق
├── SmartInvoiceManager.Core/         # النماذج وViewModels
│   ├── Models/                       # نماذج البيانات
│   ├── ViewModels/                   # ViewModels
│   └── Services/                     # واجهات الخدمات
├── SmartInvoiceManager.Data/         # طبقة الوصول للبيانات
│   ├── Services/                     # تطبيق الخدمات
│   └── ApplicationDbContext.cs       # سياق قاعدة البيانات
└── SmartInvoiceManager.Tests/        # اختبارات الوحدة
    ├── Models/                       # اختبارات النماذج
    └── Services/                     # اختبارات الخدمات
```

## متطلبات التشغيل

- Windows 10/11
- .NET 8 Runtime
- 100 MB مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### من الكود المصدري

1. **استنساخ المشروع:**
```bash
git clone [repository-url]
cd SmartInvoiceManager
```

2. **استعادة الحزم:**
```bash
dotnet restore
```

3. **بناء المشروع:**
```bash
dotnet build
```

4. **تشغيل التطبيق:**
```bash
dotnet run --project SmartInvoiceManager
```

### تشغيل الاختبارات

```bash
dotnet test
```

## الاستخدام

### إنشاء فاتورة جديدة

1. انتقل إلى قسم "الفواتير"
2. اضغط على "فاتورة جديدة"
3. اختر العميل أو أضف عميل جديد
4. أضف المنتجات/الخدمات
5. سيتم حساب الضرائب والإجمالي تلقائياً
6. احفظ الفاتورة

### إضافة عميل جديد

1. انتقل إلى قسم "العملاء"
2. اضغط على "عميل جديد"
3. أدخل معلومات العميل
4. احفظ البيانات

### تصدير التقارير

1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير والفترة الزمنية
3. اضغط على "تصدير Excel"
4. اختر مكان الحفظ

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات مع اختبارات مناسبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## خارطة الطريق

### الإصدار القادم (v1.1)
- [ ] طباعة الفواتير
- [ ] إرسال الفواتير بالبريد الإلكتروني
- [ ] دعم العملات المتعددة
- [ ] تقارير متقدمة مع الرسوم البيانية

### المستقبل
- [ ] تطبيق ويب مصاحب
- [ ] API للتكامل مع أنظمة أخرى
- [ ] دعم الفواتير الإلكترونية
- [ ] تطبيق موبايل

---

**تم تطويره بـ ❤️ للمجتمع العربي**
