{"Application": {"Name": "Smart Invoice Manager", "Version": "1.0.0", "Culture": "ar-SA"}, "Database": {"Provider": "SQLite", "ConnectionString": "Data Source=SmartInvoiceManager.db"}, "Company": {"DefaultTaxRate": 15.0, "DefaultCurrency": "SAR", "DefaultCurrencySymbol": "ر.س"}, "Updates": {"CheckForUpdatesOnStartup": true, "UpdateUrl": "https://your-update-server.com/releases", "AutoDownloadUpdates": false}, "Backup": {"AutoBackup": true, "BackupInterval": "Daily", "BackupRetentionDays": 30, "BackupPath": "%APPDATA%\\SmartInvoiceManager\\Backups"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "LogPath": "%APPDATA%\\SmartInvoiceManager\\Logs"}, "Features": {"EnableTelemetry": false, "EnableCrashReporting": true, "EnableAutoSave": true, "AutoSaveInterval": 300}}