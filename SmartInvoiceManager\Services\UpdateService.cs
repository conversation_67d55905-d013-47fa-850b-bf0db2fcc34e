using SmartInvoiceManager.Core.Services;
using Squirrel;
using System.Reflection;

namespace SmartInvoiceManager.Services;

public class UpdateService : IUpdateService
{
    private readonly string _updateUrl;

    public UpdateService()
    {
        // في التطبيق الحقيقي، يجب أن يكون هذا URL لخادم التحديثات
        _updateUrl = "https://your-update-server.com/releases";
    }

    public async Task<bool> CheckForUpdatesAsync()
    {
        try
        {
            using var manager = new UpdateManager(_updateUrl);
            var updateInfo = await manager.CheckForUpdate();
            return updateInfo.ReleasesToApply.Any();
        }
        catch (Exception)
        {
            // في حالة فشل التحقق من التحديثات
            return false;
        }
    }

    public async Task<string> GetLatestVersionAsync()
    {
        try
        {
            using var manager = new UpdateManager(_updateUrl);
            var updateInfo = await manager.CheckForUpdate();
            
            if (updateInfo.ReleasesToApply.Any())
            {
                return updateInfo.ReleasesToApply.Last().Version.ToString();
            }
            
            return GetCurrentVersion();
        }
        catch (Exception)
        {
            return GetCurrentVersion();
        }
    }

    public async Task<bool> DownloadAndInstallUpdateAsync()
    {
        try
        {
            using var manager = new UpdateManager(_updateUrl);
            var updateInfo = await manager.CheckForUpdate();
            
            if (updateInfo.ReleasesToApply.Any())
            {
                await manager.DownloadReleases(updateInfo.ReleasesToApply);
                await manager.ApplyReleases(updateInfo);
                return true;
            }
            
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> IsUpdateAvailableAsync()
    {
        return await CheckForUpdatesAsync();
    }

    public string GetCurrentVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version;
        return version?.ToString() ?? "1.0.0.0";
    }
}
