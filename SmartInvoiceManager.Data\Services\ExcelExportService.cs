using ClosedXML.Excel;
using SmartInvoiceManager.Core.Models;
using SmartInvoiceManager.Core.Services;

namespace SmartInvoiceManager.Data.Services;

public class ExcelExportService : IExcelExportService
{
    public async Task<string> ExportInvoicesAsync(IEnumerable<Invoice> invoices, string filePath)
    {
        return await Task.Run(() =>
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("الفواتير");

            // Headers
            worksheet.Cell(1, 1).Value = "رقم الفاتورة";
            worksheet.Cell(1, 2).Value = "العميل";
            worksheet.Cell(1, 3).Value = "تاريخ الفاتورة";
            worksheet.Cell(1, 4).Value = "تاريخ الاستحقاق";
            worksheet.Cell(1, 5).Value = "المبلغ الفرعي";
            worksheet.Cell(1, 6).Value = "الضريبة";
            worksheet.Cell(1, 7).Value = "الخصم";
            worksheet.Cell(1, 8).Value = "المبلغ الإجمالي";
            worksheet.Cell(1, 9).Value = "الحالة";

            // Style headers
            var headerRange = worksheet.Range(1, 1, 1, 9);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

            // Data
            int row = 2;
            foreach (var invoice in invoices)
            {
                worksheet.Cell(row, 1).Value = invoice.InvoiceNumber;
                worksheet.Cell(row, 2).Value = invoice.Customer?.Name ?? "";
                worksheet.Cell(row, 3).Value = invoice.InvoiceDate.ToString("dd/MM/yyyy");
                worksheet.Cell(row, 4).Value = invoice.DueDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 5).Value = invoice.SubTotal;
                worksheet.Cell(row, 6).Value = invoice.TaxAmount;
                worksheet.Cell(row, 7).Value = invoice.DiscountAmount;
                worksheet.Cell(row, 8).Value = invoice.TotalAmount;
                worksheet.Cell(row, 9).Value = GetStatusText(invoice.Status);
                row++;
            }

            // Auto-fit columns
            worksheet.Columns().AdjustToContents();

            workbook.SaveAs(filePath);
            return filePath;
        });
    }

    public async Task<string> ExportCustomersAsync(IEnumerable<Customer> customers, string filePath)
    {
        return await Task.Run(() =>
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("العملاء");

            // Headers
            worksheet.Cell(1, 1).Value = "اسم العميل";
            worksheet.Cell(1, 2).Value = "البريد الإلكتروني";
            worksheet.Cell(1, 3).Value = "رقم الهاتف";
            worksheet.Cell(1, 4).Value = "العنوان";
            worksheet.Cell(1, 5).Value = "الرقم الضريبي";
            worksheet.Cell(1, 6).Value = "تاريخ الإنشاء";

            // Style headers
            var headerRange = worksheet.Range(1, 1, 1, 6);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightGreen;

            // Data
            int row = 2;
            foreach (var customer in customers)
            {
                worksheet.Cell(row, 1).Value = customer.Name;
                worksheet.Cell(row, 2).Value = customer.Email ?? "";
                worksheet.Cell(row, 3).Value = customer.Phone ?? "";
                worksheet.Cell(row, 4).Value = customer.Address ?? "";
                worksheet.Cell(row, 5).Value = customer.TaxNumber ?? "";
                worksheet.Cell(row, 6).Value = customer.CreatedAt.ToString("dd/MM/yyyy");
                row++;
            }

            // Auto-fit columns
            worksheet.Columns().AdjustToContents();

            workbook.SaveAs(filePath);
            return filePath;
        });
    }

    public async Task<string> ExportMonthlyReportAsync(DateTime startDate, DateTime endDate, string filePath)
    {
        return await Task.Run(() =>
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("التقرير الشهري");

            // Title
            worksheet.Cell(1, 1).Value = $"التقرير الشهري من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}";
            worksheet.Range(1, 1, 1, 5).Merge();
            worksheet.Cell(1, 1).Style.Font.Bold = true;
            worksheet.Cell(1, 1).Style.Font.FontSize = 16;
            worksheet.Cell(1, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

            // Headers
            worksheet.Cell(3, 1).Value = "الشهر";
            worksheet.Cell(3, 2).Value = "عدد الفواتير";
            worksheet.Cell(3, 3).Value = "إجمالي المبيعات";
            worksheet.Cell(3, 4).Value = "إجمالي الضرائب";
            worksheet.Cell(3, 5).Value = "صافي الربح";

            // Style headers
            var headerRange = worksheet.Range(3, 1, 3, 5);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightYellow;

            // Sample data (in real implementation, this would come from the database)
            var sampleData = new[]
            {
                new { Month = "يناير 2024", InvoiceCount = 8, TotalSales = 45000.00m, TotalTax = 6750.00m, NetProfit = 38250.00m },
                new { Month = "فبراير 2024", InvoiceCount = 12, TotalSales = 62000.00m, TotalTax = 9300.00m, NetProfit = 52700.00m },
                new { Month = "مارس 2024", InvoiceCount = 5, TotalSales = 18000.00m, TotalTax = 2700.00m, NetProfit = 15300.00m }
            };

            int row = 4;
            foreach (var data in sampleData)
            {
                worksheet.Cell(row, 1).Value = data.Month;
                worksheet.Cell(row, 2).Value = data.InvoiceCount;
                worksheet.Cell(row, 3).Value = data.TotalSales;
                worksheet.Cell(row, 4).Value = data.TotalTax;
                worksheet.Cell(row, 5).Value = data.NetProfit;
                row++;
            }

            // Auto-fit columns
            worksheet.Columns().AdjustToContents();

            workbook.SaveAs(filePath);
            return filePath;
        });
    }

    private static string GetStatusText(InvoiceStatus status)
    {
        return status switch
        {
            InvoiceStatus.Draft => "مسودة",
            InvoiceStatus.Sent => "مرسلة",
            InvoiceStatus.Paid => "مدفوعة",
            InvoiceStatus.Overdue => "متأخرة",
            InvoiceStatus.Cancelled => "ملغاة",
            _ => "غير محدد"
        };
    }
}
