using SmartInvoiceManager.Core.Models;
using System.Globalization;
using System.Windows.Data;

namespace SmartInvoiceManager.Converters;

public class StatusConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Draft => "مسودة",
                InvoiceStatus.Sent => "مرسلة",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.Overdue => "متأخرة",
                InvoiceStatus.Cancelled => "ملغاة",
                _ => "غير محدد"
            };
        }

        return "غير محدد";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
