<Page x:Class="SmartInvoiceManager.Views.DashboardView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="DashboardView">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Welcome Section -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="مرحباً بك في نظام إدارة الفواتير الذكي" 
                             FontSize="24" 
                             FontWeight="Bold"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="إدارة شاملة وذكية لجميع فواتيرك وعملائك" 
                             FontSize="16" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             HorizontalAlignment="Center"
                             Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Invoices Card -->
                <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Receipt" 
                                                   Width="32" Height="32" 
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي الفواتير" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="10,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock x:Name="TotalInvoicesText" 
                                 Text="0" 
                                 FontSize="28" 
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Revenue Card -->
                <materialDesign:Card Grid.Column="1" Margin="5,0,5,0" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="32" Height="32" 
                                                   Foreground="Green"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي الإيرادات" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="10,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock x:Name="TotalRevenueText" 
                                 Text="0 ر.س" 
                                 FontSize="28" 
                                 FontWeight="Bold"
                                 Foreground="Green"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Customers Card -->
                <materialDesign:Card Grid.Column="2" Margin="5,0,5,0" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                   Width="32" Height="32" 
                                                   Foreground="Orange"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="إجمالي العملاء" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="10,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock x:Name="TotalCustomersText" 
                                 Text="0" 
                                 FontSize="28" 
                                 FontWeight="Bold"
                                 Foreground="Orange"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Pending Invoices Card -->
                <materialDesign:Card Grid.Column="3" Margin="10,0,0,0" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="ClockOutline" 
                                                   Width="32" Height="32" 
                                                   Foreground="Red"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="فواتير معلقة" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="10,0,0,0"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock x:Name="PendingInvoicesText" 
                                 Text="0" 
                                 FontSize="28" 
                                 FontWeight="Bold"
                                 Foreground="Red"
                                 HorizontalAlignment="Center"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" Padding="20">
                <StackPanel>
                    <TextBlock Text="الإجراءات السريعة" 
                             FontSize="20" 
                             FontWeight="Bold"
                             Margin="0,0,0,20"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60"
                                Margin="0,0,10,0"
                                Background="{DynamicResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" 
                                                       Width="24" Height="24" 
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="إنشاء فاتورة جديدة" 
                                         Margin="10,0,0,0" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="1" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60"
                                Margin="5,0,5,0"
                                Background="Orange">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountPlus" 
                                                       Width="24" Height="24" 
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="إضافة عميل جديد" 
                                         Margin="10,0,0,0" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="2" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60"
                                Margin="10,0,0,0"
                                Background="Green">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExport" 
                                                       Width="24" Height="24" 
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="تصدير التقارير" 
                                         Margin="10,0,0,0" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ScrollViewer>
</Page>
