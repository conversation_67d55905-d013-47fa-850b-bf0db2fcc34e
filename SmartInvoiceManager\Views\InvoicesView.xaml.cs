using System.Windows;
using System.Windows.Controls;

namespace SmartInvoiceManager.Views;

/// <summary>
/// Interaction logic for InvoicesView.xaml
/// </summary>
public partial class InvoicesView : Page
{
    public InvoicesView()
    {
        InitializeComponent();
        LoadInvoices();
    }

    private async void LoadInvoices()
    {
        // TODO: Load actual invoices from service
        // For now, using sample data
        var sampleInvoices = new[]
        {
            new { 
                Id = 1, 
                InvoiceNumber = "INV-202401-0001", 
                Customer = new { Name = "شركة الأمل للتجارة" },
                InvoiceDate = DateTime.Now.AddDays(-5),
                DueDate = DateTime.Now.AddDays(25),
                TotalAmount = 15000.00m,
                Status = 1
            },
            new { 
                Id = 2, 
                InvoiceNumber = "INV-202401-0002", 
                Customer = new { Name = "مؤسسة النور للخدمات" },
                InvoiceDate = DateTime.Now.AddDays(-3),
                DueDate = DateTime.Now.AddDays(27),
                TotalAmount = 8500.00m,
                Status = 2
            }
        };

        InvoicesDataGrid.ItemsSource = sampleInvoices;
    }

    private void NewInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        // TODO: Navigate to new invoice page
        MessageBox.Show("سيتم فتح صفحة إنشاء فاتورة جديدة", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ViewInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int invoiceId)
        {
            MessageBox.Show($"عرض الفاتورة رقم: {invoiceId}", "عرض الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int invoiceId)
        {
            MessageBox.Show($"تعديل الفاتورة رقم: {invoiceId}", "تعديل الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int invoiceId)
        {
            MessageBox.Show($"طباعة الفاتورة رقم: {invoiceId}", "طباعة الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int invoiceId)
        {
            var result = MessageBox.Show($"هل أنت متأكد من حذف الفاتورة رقم: {invoiceId}؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم حذف الفاتورة بنجاح", "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadInvoices(); // Reload the list
            }
        }
    }
}
