# دليل التثبيت والإعداد - Smart Invoice Manager

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل:** Windows 10 (الإصدار 1809 أو أحدث)
- **المعالج:** Intel Core i3 أو AMD Ryzen 3
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 500 MB مساحة فارغة
- **.NET Runtime:** .NET 8.0 أو أحدث

### المستحسن
- **نظام التشغيل:** Windows 11
- **المعالج:** Intel Core i5 أو AMD Ryzen 5
- **الذاكرة:** 8 GB RAM
- **مساحة القرص:** 2 GB مساحة فارغة
- **الشاشة:** دقة 1920x1080 أو أعلى

## التثبيت

### الطريقة الأولى: تشغيل من الكود المصدري

1. **تثبيت .NET 8 SDK:**
   - قم بتحميل وتثبيت .NET 8 SDK من [الموقع الرسمي](https://dotnet.microsoft.com/download)

2. **استنساخ المشروع:**
   ```bash
   git clone [repository-url]
   cd SmartInvoiceManager
   ```

3. **استعادة الحزم:**
   ```bash
   dotnet restore
   ```

4. **بناء المشروع:**
   ```bash
   dotnet build --configuration Release
   ```

5. **تشغيل التطبيق:**
   ```bash
   dotnet run --project SmartInvoiceManager
   ```

### الطريقة الثانية: استخدام ملف البناء

1. **تشغيل ملف البناء:**
   ```powershell
   .\build.ps1 -Configuration Release -CreateInstaller
   ```

2. **تشغيل التطبيق:**
   - انتقل إلى مجلد `publish`
   - شغل ملف `SmartInvoiceManager.exe`

## الإعداد الأولي

### 1. إعداد معلومات الشركة

عند تشغيل التطبيق لأول مرة:

1. انتقل إلى **الإعدادات** من القائمة الجانبية
2. أدخل معلومات شركتك:
   - اسم الشركة
   - الرقم الضريبي
   - العنوان
   - معلومات الاتصال
3. ارفع شعار الشركة (اختياري)
4. اضغط **حفظ الإعدادات**

### 2. إعداد الضرائب

1. في صفحة الإعدادات، قسم **إعدادات الضرائب**
2. أدخل نسبة الضريبة الافتراضية (15% للسعودية)
3. اختر العملة المناسبة

### 3. إضافة العملاء الأوائل

1. انتقل إلى قسم **العملاء**
2. اضغط **عميل جديد**
3. أدخل معلومات العميل
4. احفظ البيانات

### 4. إضافة المنتجات/الخدمات

1. انتقل إلى قسم **المنتجات**
2. اضغط **منتج جديد**
3. أدخل تفاصيل المنتج/الخدمة
4. احفظ البيانات

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### 1. التطبيق لا يبدأ
**الحل:**
- تأكد من تثبيت .NET 8 Runtime
- تشغيل التطبيق كمدير (Run as Administrator)
- تحقق من ملفات السجل في `%APPDATA%\SmartInvoiceManager\Logs`

#### 2. خطأ في قاعدة البيانات
**الحل:**
- احذف ملف `SmartInvoiceManager.db` من مجلد التطبيق
- أعد تشغيل التطبيق لإنشاء قاعدة بيانات جديدة

#### 3. مشاكل في الطباعة
**الحل:**
- تأكد من تثبيت طابعة افتراضية
- تحقق من إعدادات الطابعة في Windows

#### 4. مشاكل في التصدير إلى Excel
**الحل:**
- تأكد من عدم فتح ملف Excel المراد الكتابة عليه
- تحقق من صلاحيات الكتابة في المجلد المحدد

### ملفات السجل

يحفظ التطبيق ملفات السجل في:
```
%APPDATA%\SmartInvoiceManager\Logs\
```

### النسخ الاحتياطية

يتم حفظ النسخ الاحتياطية التلقائية في:
```
%APPDATA%\SmartInvoiceManager\Backups\
```

## التحديثات

### التحديث التلقائي
- يتحقق التطبيق من التحديثات عند بدء التشغيل
- يمكن تفعيل/إلغاء هذه الميزة من الإعدادات

### التحديث اليدوي
1. انتقل إلى **الإعدادات**
2. اضغط **التحقق من التحديثات**
3. اتبع التعليمات لتحميل وتثبيت التحديث

## الأمان والخصوصية

### حماية البيانات
- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات لخوادم خارجية
- يُنصح بعمل نسخ احتياطية دورية

### كلمات المرور
- حالياً لا يدعم التطبيق كلمات المرور
- يُنصح بحماية الجهاز بكلمة مرور

## الدعم الفني

### الحصول على المساعدة
- راجع ملف [README.md](README.md) للمعلومات الأساسية
- ابحث في [Issues](https://github.com/your-repo/issues) الموجودة
- أنشئ Issue جديد للمشاكل غير المحلولة

### معلومات مفيدة عند طلب الدعم
- إصدار التطبيق
- إصدار Windows
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج المشكلة
- ملفات السجل (إن أمكن)

## الترقية من إصدارات سابقة

### من الإصدار 1.0 إلى 1.1
1. أنشئ نسخة احتياطية من قاعدة البيانات
2. ثبت الإصدار الجديد
3. شغل التطبيق - سيتم ترقية قاعدة البيانات تلقائياً

---

**ملاحظة:** هذا الدليل يُحدث باستمرار. تأكد من مراجعة أحدث إصدار.
