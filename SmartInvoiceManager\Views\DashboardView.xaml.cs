using System.Windows.Controls;

namespace SmartInvoiceManager.Views;

/// <summary>
/// Interaction logic for DashboardView.xaml
/// </summary>
public partial class DashboardView : Page
{
    public DashboardView()
    {
        InitializeComponent();
        LoadDashboardData();
    }

    private async void LoadDashboardData()
    {
        // TODO: Load actual data from services
        // For now, using sample data
        TotalInvoicesText.Text = "25";
        TotalRevenueText.Text = "125,000 ر.س";
        TotalCustomersText.Text = "15";
        PendingInvoicesText.Text = "3";
    }
}
