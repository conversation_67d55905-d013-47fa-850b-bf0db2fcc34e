﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Smart Invoice Manager</AssemblyTitle>
    <AssemblyDescription>نظام إدارة الفواتير الذكي</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="Squirrel.Windows" Version="2.0.1" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SmartInvoiceManager.Core\SmartInvoiceManager.Core.csproj" />
    <ProjectReference Include="..\SmartInvoiceManager.Data\SmartInvoiceManager.Data.csproj" />
  </ItemGroup>

</Project>
