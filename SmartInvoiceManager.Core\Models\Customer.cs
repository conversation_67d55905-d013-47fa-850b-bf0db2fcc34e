using System.ComponentModel.DataAnnotations;

namespace SmartInvoiceManager.Core.Models;

public class Customer
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم العميل مطلوب")]
    [StringLength(100, ErrorMessage = "اسم العميل يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    public string? Email { get; set; }

    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string? Phone { get; set; }

    [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
    public string? Address { get; set; }

    [StringLength(20, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 20 حرف")]
    public string? TaxNumber { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }

    // Navigation Properties
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
