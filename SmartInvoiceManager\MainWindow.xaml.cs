﻿using SmartInvoiceManager.Core.ViewModels;
using SmartInvoiceManager.Views;
using System.Windows;
using System.Windows.Controls;

namespace SmartInvoiceManager;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;

    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        DataContext = _viewModel;

        // Load dashboard by default
        LoadDashboard();
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        LoadDashboard();
        PageTitleTextBlock.Text = "لوحة التحكم";
        _viewModel.SelectedMenuItem = "Dashboard";
    }

    private void InvoicesButton_Click(object sender, RoutedEventArgs e)
    {
        MainContentFrame.Navigate(new InvoicesView());
        PageTitleTextBlock.Text = "الفواتير";
        _viewModel.SelectedMenuItem = "Invoices";
    }

    private void CustomersButton_Click(object sender, RoutedEventArgs e)
    {
        MainContentFrame.Navigate(new CustomersView());
        PageTitleTextBlock.Text = "العملاء";
        _viewModel.SelectedMenuItem = "Customers";
    }

    private void ProductsButton_Click(object sender, RoutedEventArgs e)
    {
        MainContentFrame.Navigate(new ProductsView());
        PageTitleTextBlock.Text = "المنتجات";
        _viewModel.SelectedMenuItem = "Products";
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        MainContentFrame.Navigate(new ReportsView());
        PageTitleTextBlock.Text = "التقارير";
        _viewModel.SelectedMenuItem = "Reports";
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        MainContentFrame.Navigate(new SettingsView());
        PageTitleTextBlock.Text = "الإعدادات";
        _viewModel.SelectedMenuItem = "Settings";
    }

    private void LoadDashboard()
    {
        MainContentFrame.Navigate(new DashboardView());
    }
}