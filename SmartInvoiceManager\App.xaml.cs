﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SmartInvoiceManager.Core.Services;
using SmartInvoiceManager.Core.ViewModels;
using SmartInvoiceManager.Data;
using SmartInvoiceManager.Data.Services;
using System.IO;
using System.Windows;

namespace SmartInvoiceManager;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Database
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                         "SmartInvoiceManager", "database.db");
                Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);

                services.AddDbContext<ApplicationDbContext>(options =>
                    options.UseSqlite($"Data Source={dbPath}"));

                // Services
                services.AddScoped<ICustomerService, CustomerService>();
                services.AddScoped<IInvoiceService, InvoiceService>();

                // ViewModels
                services.AddTransient<MainViewModel>();

                // Windows
                services.AddTransient<MainWindow>();
            })
            .Build();

        // Initialize database
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            context.Database.EnsureCreated();
        }

        // Show main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}

