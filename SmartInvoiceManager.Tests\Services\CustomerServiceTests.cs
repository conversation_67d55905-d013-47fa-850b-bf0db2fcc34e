using Microsoft.EntityFrameworkCore;
using SmartInvoiceManager.Core.Models;
using SmartInvoiceManager.Data;
using SmartInvoiceManager.Data.Services;

namespace SmartInvoiceManager.Tests.Services;

public class CustomerServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CustomerService _customerService;

    public CustomerServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _customerService = new CustomerService(_context);
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateCustomer_WhenValidDataProvided()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "شركة الاختبار",
            Email = "<EMAIL>",
            Phone = "0501234567",
            Address = "الرياض، المملكة العربية السعودية"
        };

        // Act
        var result = await _customerService.CreateAsync(customer);

        // Assert
        Assert.NotEqual(0, result.Id);
        Assert.Equal("شركة الاختبار", result.Name);
        Assert.True(result.CreatedAt <= DateTime.Now);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnCustomer_WhenCustomerExists()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "عميل تجريبي",
            Email = "<EMAIL>"
        };

        await _customerService.CreateAsync(customer);

        // Act
        var result = await _customerService.GetByIdAsync(customer.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("عميل تجريبي", result.Name);
        Assert.Equal("<EMAIL>", result.Email);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenCustomerDoesNotExist()
    {
        // Act
        var result = await _customerService.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateCustomer_WhenValidDataProvided()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "عميل قديم",
            Email = "<EMAIL>"
        };

        await _customerService.CreateAsync(customer);

        // Act
        customer.Name = "عميل محدث";
        customer.Email = "<EMAIL>";
        var result = await _customerService.UpdateAsync(customer);

        // Assert
        Assert.Equal("عميل محدث", result.Name);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.NotNull(result.UpdatedAt);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnTrue_WhenCustomerExistsAndHasNoInvoices()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "عميل للحذف",
            Email = "<EMAIL>"
        };

        await _customerService.CreateAsync(customer);

        // Act
        var result = await _customerService.DeleteAsync(customer.Id);

        // Assert
        Assert.True(result);
        
        var deletedCustomer = await _customerService.GetByIdAsync(customer.Id);
        Assert.Null(deletedCustomer);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnFalse_WhenCustomerDoesNotExist()
    {
        // Act
        var result = await _customerService.DeleteAsync(999);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task SearchAsync_ShouldReturnMatchingCustomers_WhenSearchTermProvided()
    {
        // Arrange
        var customers = new[]
        {
            new Customer { Name = "شركة الأمل", Email = "<EMAIL>" },
            new Customer { Name = "مؤسسة النور", Email = "<EMAIL>" },
            new Customer { Name = "شركة الفجر", Email = "<EMAIL>" }
        };

        foreach (var customer in customers)
        {
            await _customerService.CreateAsync(customer);
        }

        // Act
        var result = await _customerService.SearchAsync("شركة");

        // Assert
        Assert.Equal(2, result.Count());
        Assert.All(result, c => Assert.Contains("شركة", c.Name));
    }

    [Fact]
    public async Task SearchAsync_ShouldReturnAllCustomers_WhenSearchTermIsEmpty()
    {
        // Arrange
        var customers = new[]
        {
            new Customer { Name = "عميل 1", Email = "<EMAIL>" },
            new Customer { Name = "عميل 2", Email = "<EMAIL>" }
        };

        foreach (var customer in customers)
        {
            await _customerService.CreateAsync(customer);
        }

        // Act
        var result = await _customerService.SearchAsync("");

        // Assert
        Assert.Equal(2, result.Count());
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnTrue_WhenCustomerExists()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "عميل موجود",
            Email = "<EMAIL>"
        };

        await _customerService.CreateAsync(customer);

        // Act
        var result = await _customerService.ExistsAsync(customer.Id);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnFalse_WhenCustomerDoesNotExist()
    {
        // Act
        var result = await _customerService.ExistsAsync(999);

        // Assert
        Assert.False(result);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
