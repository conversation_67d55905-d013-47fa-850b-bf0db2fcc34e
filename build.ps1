# Build script for Smart Invoice Manager
# PowerShell script to build and package the application

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\publish",
    [switch]$CreateInstaller
)

Write-Host "Building Smart Invoice Manager..." -ForegroundColor Green

# Clean previous builds
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
    Write-Host "Cleaned previous build output" -ForegroundColor Yellow
}

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Blue
dotnet restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to restore packages"
    exit 1
}

# Build solution
Write-Host "Building solution..." -ForegroundColor Blue
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

# Run tests
Write-Host "Running tests..." -ForegroundColor Blue
dotnet test --configuration $Configuration --no-build --verbosity normal

if ($LASTEXITCODE -ne 0) {
    Write-Warning "Some tests failed, but continuing with build"
}

# Publish application
Write-Host "Publishing application..." -ForegroundColor Blue
dotnet publish SmartInvoiceManager/SmartInvoiceManager.csproj `
    --configuration $Configuration `
    --output $OutputPath `
    --self-contained false `
    --runtime win-x64

if ($LASTEXITCODE -ne 0) {
    Write-Error "Publish failed"
    exit 1
}

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output location: $OutputPath" -ForegroundColor Cyan

if ($CreateInstaller) {
    Write-Host "Creating installer package..." -ForegroundColor Blue
    # Here you would add Squirrel packaging commands
    # squirrel --releasify SmartInvoiceManager.1.0.0.nupkg
    Write-Host "Installer creation would be implemented here" -ForegroundColor Yellow
}

Write-Host "Done!" -ForegroundColor Green
