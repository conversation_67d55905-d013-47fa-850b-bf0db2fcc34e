<Page x:Class="SmartInvoiceManager.Views.InvoicesView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="InvoicesView">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="إدارة الفواتير" 
                     FontSize="24" 
                     FontWeight="Bold"
                     VerticalAlignment="Center"/>

            <Button Grid.Column="1" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Click="NewInvoiceButton_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" 
                                           Width="20" Height="20" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="فاتورة جديدة" 
                             Margin="5,0,0,0" 
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- Search and Filter -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" 
                         x:Name="SearchTextBox"
                         materialDesign:HintAssist.Hint="البحث في الفواتير..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,10,0"/>

                <ComboBox Grid.Column="1" 
                          x:Name="StatusFilterComboBox"
                          materialDesign:HintAssist.Hint="حالة الفاتورة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Width="150"
                          Margin="0,0,10,0">
                    <ComboBoxItem Content="جميع الحالات"/>
                    <ComboBoxItem Content="مسودة"/>
                    <ComboBoxItem Content="مرسلة"/>
                    <ComboBoxItem Content="مدفوعة"/>
                    <ComboBoxItem Content="متأخرة"/>
                    <ComboBoxItem Content="ملغاة"/>
                </ComboBox>

                <Button Grid.Column="2" 
                        Style="{StaticResource MaterialDesignIconButton}"
                        ToolTip="بحث">
                    <materialDesign:PackIcon Kind="Magnify"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Invoices DataGrid -->
        <materialDesign:Card Grid.Row="2">
            <DataGrid x:Name="InvoicesDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" 
                                        Binding="{Binding InvoiceNumber}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="العميل" 
                                        Binding="{Binding Customer.Name}" 
                                        Width="150"/>
                    <DataGridTextColumn Header="تاريخ الفاتورة" 
                                        Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="تاريخ الاستحقاق" 
                                        Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="المبلغ الإجمالي" 
                                        Binding="{Binding TotalAmount, StringFormat=N2}" 
                                        Width="120"/>
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="10" 
                                        Padding="8,4"
                                        HorizontalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="0">
                                                    <Setter Property="Background" Value="LightGray"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="1">
                                                    <Setter Property="Background" Value="LightBlue"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="2">
                                                    <Setter Property="Background" Value="LightGreen"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="3">
                                                    <Setter Property="Background" Value="LightCoral"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="4">
                                                    <Setter Property="Background" Value="LightPink"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding Status, Converter={StaticResource StatusConverter}}" 
                                               FontSize="12" 
                                               FontWeight="Bold"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="عرض"
                                            Click="ViewInvoiceButton_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="تعديل"
                                            Click="EditInvoiceButton_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="طباعة"
                                            Click="PrintInvoiceButton_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="حذف"
                                            Click="DeleteInvoiceButton_Click"
                                            Tag="{Binding Id}"
                                            Foreground="Red">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
