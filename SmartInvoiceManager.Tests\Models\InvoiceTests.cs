using SmartInvoiceManager.Core.Models;

namespace SmartInvoiceManager.Tests.Models;

public class InvoiceTests
{
    [Fact]
    public void Invoice_CalculateTotals_ShouldCalculateCorrectly()
    {
        // Arrange
        var invoice = new Invoice
        {
            TaxRate = 15,
            DiscountAmount = 100
        };

        var item1 = new InvoiceItem
        {
            Quantity = 2,
            UnitPrice = 500
        };

        var item2 = new InvoiceItem
        {
            Quantity = 1,
            UnitPrice = 1000
        };

        invoice.Items.Add(item1);
        invoice.Items.Add(item2);

        // Act
        invoice.CalculateTotals();

        // Assert
        Assert.Equal(2000, invoice.SubTotal); // (2 * 500) + (1 * 1000)
        Assert.Equal(300, invoice.TaxAmount); // 2000 * 0.15
        Assert.Equal(2200, invoice.TotalAmount); // 2000 + 300 - 100
    }

    [Fact]
    public void Invoice_CalculateTotals_WithNoItems_ShouldReturnZero()
    {
        // Arrange
        var invoice = new Invoice
        {
            TaxRate = 15,
            DiscountAmount = 0
        };

        // Act
        invoice.CalculateTotals();

        // Assert
        Assert.Equal(0, invoice.SubTotal);
        Assert.Equal(0, invoice.TaxAmount);
        Assert.Equal(0, invoice.TotalAmount);
    }

    [Fact]
    public void Invoice_CalculateTotals_WithDiscount_ShouldSubtractFromTotal()
    {
        // Arrange
        var invoice = new Invoice
        {
            TaxRate = 10,
            DiscountAmount = 50
        };

        var item = new InvoiceItem
        {
            Quantity = 1,
            UnitPrice = 1000
        };

        invoice.Items.Add(item);

        // Act
        invoice.CalculateTotals();

        // Assert
        Assert.Equal(1000, invoice.SubTotal);
        Assert.Equal(100, invoice.TaxAmount); // 1000 * 0.10
        Assert.Equal(1050, invoice.TotalAmount); // 1000 + 100 - 50
    }

    [Theory]
    [InlineData(0)]
    [InlineData(5)]
    [InlineData(15)]
    [InlineData(20)]
    public void Invoice_CalculateTotals_WithDifferentTaxRates_ShouldCalculateCorrectly(decimal taxRate)
    {
        // Arrange
        var invoice = new Invoice
        {
            TaxRate = taxRate,
            DiscountAmount = 0
        };

        var item = new InvoiceItem
        {
            Quantity = 1,
            UnitPrice = 1000
        };

        invoice.Items.Add(item);

        // Act
        invoice.CalculateTotals();

        // Assert
        var expectedTaxAmount = 1000 * (taxRate / 100);
        var expectedTotal = 1000 + expectedTaxAmount;

        Assert.Equal(1000, invoice.SubTotal);
        Assert.Equal(expectedTaxAmount, invoice.TaxAmount);
        Assert.Equal(expectedTotal, invoice.TotalAmount);
    }

    [Fact]
    public void InvoiceItem_TotalPrice_ShouldCalculateCorrectly()
    {
        // Arrange
        var item = new InvoiceItem
        {
            Quantity = 3.5m,
            UnitPrice = 250.75m
        };

        // Act
        var totalPrice = item.TotalPrice;

        // Assert
        Assert.Equal(877.625m, totalPrice); // 3.5 * 250.75
    }

    [Fact]
    public void Invoice_DefaultValues_ShouldBeSetCorrectly()
    {
        // Arrange & Act
        var invoice = new Invoice();

        // Assert
        Assert.Equal(InvoiceStatus.Draft, invoice.Status);
        Assert.Equal(15, invoice.TaxRate); // Default Saudi VAT rate
        Assert.True(invoice.CreatedAt <= DateTime.Now);
        Assert.True(invoice.InvoiceDate <= DateTime.Now);
        Assert.NotNull(invoice.Items);
        Assert.Empty(invoice.Items);
    }
}
