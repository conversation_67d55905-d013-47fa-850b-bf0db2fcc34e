using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartInvoiceManager.Core.Models;

public class InvoiceItem
{
    public int Id { get; set; }

    [Required(ErrorMessage = "الفاتورة مطلوبة")]
    public int InvoiceId { get; set; }

    [Required(ErrorMessage = "المنتج مطلوب")]
    public int ProductId { get; set; }

    [Required(ErrorMessage = "الكمية مطلوبة")]
    [Range(0.01, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Quantity { get; set; }

    [Required(ErrorMessage = "سعر الوحدة مطلوب")]
    [Range(0.01, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من صفر")]
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitPrice { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalPrice => Quantity * UnitPrice;

    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }

    // Navigation Properties
    public virtual Invoice Invoice { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
}
