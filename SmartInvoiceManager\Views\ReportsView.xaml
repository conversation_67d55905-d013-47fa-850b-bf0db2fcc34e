<Page x:Class="SmartInvoiceManager.Views.ReportsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="ReportsView">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                 Text="التقارير والإحصائيات" 
                 FontSize="24" 
                 FontWeight="Bold"
                 Margin="0,0,0,20"/>

        <!-- Report Filters -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <DatePicker Grid.Column="0" 
                           materialDesign:HintAssist.Hint="من تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="0,0,10,0"/>

                <DatePicker Grid.Column="1" 
                           materialDesign:HintAssist.Hint="إلى تاريخ"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                           Margin="0,0,10,0"/>

                <ComboBox Grid.Column="2" 
                         materialDesign:HintAssist.Hint="نوع التقرير"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         Margin="0,0,10,0">
                    <ComboBoxItem Content="تقرير شهري"/>
                    <ComboBoxItem Content="تقرير سنوي"/>
                    <ComboBoxItem Content="تقرير العملاء"/>
                    <ComboBoxItem Content="تقرير المنتجات"/>
                </ComboBox>

                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" 
                                                   Width="16" Height="16" 
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="عرض" 
                                     Margin="5,0,0,0" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="Green">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" 
                                                   Width="16" Height="16" 
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="تصدير Excel" 
                                     Margin="5,0,0,0" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Reports Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Report Table -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0">
                <DataGrid x:Name="ReportsDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الشهر" Binding="{Binding Month}" Width="100"/>
                        <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120"/>
                        <DataGridTextColumn Header="إجمالي المبيعات" Binding="{Binding TotalSales, StringFormat=N2}" Width="150"/>
                        <DataGridTextColumn Header="إجمالي الضرائب" Binding="{Binding TotalTax, StringFormat=N2}" Width="150"/>
                        <DataGridTextColumn Header="صافي الربح" Binding="{Binding NetProfit, StringFormat=N2}" Width="150"/>
                    </DataGrid.Columns>
                </DataGrid>
            </materialDesign:Card>

            <!-- Summary Cards -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <materialDesign:Card Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        <TextBlock Text="إجمالي المبيعات" 
                                 FontWeight="Bold" 
                                 FontSize="14"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="125,000 ر.س" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Green"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        <TextBlock Text="إجمالي الضرائب" 
                                 FontWeight="Bold" 
                                 FontSize="14"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="18,750 ر.س" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Orange"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Margin="0,0,0,15" Padding="15">
                    <StackPanel>
                        <TextBlock Text="عدد الفواتير" 
                                 FontWeight="Bold" 
                                 FontSize="14"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="25" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </materialDesign:Card>

                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="متوسط قيمة الفاتورة" 
                                 FontWeight="Bold" 
                                 FontSize="14"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="5,000 ر.س" 
                                 FontSize="24" 
                                 FontWeight="Bold"
                                 Foreground="Purple"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
