using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartInvoiceManager.Core.Models;

public class Product
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم المنتج مطلوب")]
    [StringLength(100, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "وصف المنتج يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "سعر المنتج مطلوب")]
    [Range(0.01, double.MaxValue, ErrorMessage = "سعر المنتج يجب أن يكون أكبر من صفر")]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Price { get; set; }

    [StringLength(20, ErrorMessage = "وحدة القياس يجب أن تكون أقل من 20 حرف")]
    public string Unit { get; set; } = "قطعة";

    [Range(0, int.MaxValue, ErrorMessage = "الكمية المتاحة يجب أن تكون أكبر من أو تساوي صفر")]
    public int StockQuantity { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }

    // Navigation Properties
    public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
}
