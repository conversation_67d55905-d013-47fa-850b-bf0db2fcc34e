using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace SmartInvoiceManager.Core.ViewModels;

public partial class MainViewModel : BaseViewModel
{
    [ObservableProperty]
    private object? currentView;

    [ObservableProperty]
    private string selectedMenuItem = "Dashboard";

    public MainViewModel()
    {
        Title = "Smart Invoice Manager";
    }

    [RelayCommand]
    private void NavigateToInvoices()
    {
        SelectedMenuItem = "Invoices";
        // Navigation logic will be implemented in the WPF project
    }

    [RelayCommand]
    private void NavigateToCustomers()
    {
        SelectedMenuItem = "Customers";
        // Navigation logic will be implemented in the WPF project
    }

    [RelayCommand]
    private void NavigateToProducts()
    {
        SelectedMenuItem = "Products";
        // Navigation logic will be implemented in the WPF project
    }

    [RelayCommand]
    private void NavigateToReports()
    {
        SelectedMenuItem = "Reports";
        // Navigation logic will be implemented in the WPF project
    }

    [RelayCommand]
    private void NavigateToSettings()
    {
        SelectedMenuItem = "Settings";
        // Navigation logic will be implemented in the WPF project
    }

    [RelayCommand]
    private void NavigateToDashboard()
    {
        SelectedMenuItem = "Dashboard";
        // Navigation logic will be implemented in the WPF project
    }
}
