using Microsoft.EntityFrameworkCore;
using SmartInvoiceManager.Core.Models;
using SmartInvoiceManager.Core.Services;

namespace SmartInvoiceManager.Data.Services;

public class CustomerService : ICustomerService
{
    private readonly ApplicationDbContext _context;

    public CustomerService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Customer>> GetAllAsync()
    {
        return await _context.Customers
            .OrderBy(c => c.Name)
            .ToListAsync();
    }

    public async Task<Customer?> GetByIdAsync(int id)
    {
        return await _context.Customers
            .Include(c => c.Invoices)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Customer> CreateAsync(Customer customer)
    {
        customer.CreatedAt = DateTime.Now;
        _context.Customers.Add(customer);
        await _context.SaveChangesAsync();
        return customer;
    }

    public async Task<Customer> UpdateAsync(Customer customer)
    {
        customer.UpdatedAt = DateTime.Now;
        _context.Customers.Update(customer);
        await _context.SaveChangesAsync();
        return customer;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var customer = await _context.Customers.FindAsync(id);
        if (customer == null)
            return false;

        // Check if customer has invoices
        var hasInvoices = await _context.Invoices.AnyAsync(i => i.CustomerId == id);
        if (hasInvoices)
            throw new InvalidOperationException("لا يمكن حذف العميل لأنه مرتبط بفواتير موجودة");

        _context.Customers.Remove(customer);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _context.Customers.AnyAsync(c => c.Id == id);
    }

    public async Task<IEnumerable<Customer>> SearchAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return await GetAllAsync();

        return await _context.Customers
            .Where(c => c.Name.Contains(searchTerm) ||
                       (c.Email != null && c.Email.Contains(searchTerm)) ||
                       (c.Phone != null && c.Phone.Contains(searchTerm)))
            .OrderBy(c => c.Name)
            .ToListAsync();
    }
}
