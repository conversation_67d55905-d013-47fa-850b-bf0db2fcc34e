using System.ComponentModel.DataAnnotations;

namespace SmartInvoiceManager.Core.Models;

public class Company
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم الشركة مطلوب")]
    [StringLength(200, ErrorMessage = "اسم الشركة يجب أن يكون أقل من 200 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
    public string? Address { get; set; }

    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string? Phone { get; set; }

    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    public string? Email { get; set; }

    [StringLength(100, ErrorMessage = "الموقع الإلكتروني يجب أن يكون أقل من 100 حرف")]
    public string? Website { get; set; }

    [Required(ErrorMessage = "الرقم الضريبي مطلوب")]
    [StringLength(20, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 20 حرف")]
    public string TaxNumber { get; set; } = string.Empty;

    [StringLength(20, ErrorMessage = "السجل التجاري يجب أن يكون أقل من 20 حرف")]
    public string? CommercialRegister { get; set; }

    public string? LogoPath { get; set; }

    [Range(0, 100, ErrorMessage = "نسبة الضريبة الافتراضية يجب أن تكون بين 0 و 100")]
    public decimal DefaultTaxRate { get; set; } = 15; // Default VAT rate in Saudi Arabia

    public string Currency { get; set; } = "ريال سعودي";
    public string CurrencySymbol { get; set; } = "ر.س";

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }
}
