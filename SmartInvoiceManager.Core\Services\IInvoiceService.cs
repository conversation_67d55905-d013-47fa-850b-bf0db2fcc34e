using SmartInvoiceManager.Core.Models;

namespace SmartInvoiceManager.Core.Services;

public interface IInvoiceService
{
    Task<IEnumerable<Invoice>> GetAllAsync();
    Task<Invoice?> GetByIdAsync(int id);
    Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber);
    Task<Invoice> CreateAsync(Invoice invoice);
    Task<Invoice> UpdateAsync(Invoice invoice);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<IEnumerable<Invoice>> GetByCustomerIdAsync(int customerId);
    Task<IEnumerable<Invoice>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<Invoice>> GetByStatusAsync(InvoiceStatus status);
    Task<string> GenerateInvoiceNumberAsync();
    Task<decimal> GetTotalRevenueAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<int> GetInvoiceCountAsync(DateTime? startDate = null, DateTime? endDate = null);
}
