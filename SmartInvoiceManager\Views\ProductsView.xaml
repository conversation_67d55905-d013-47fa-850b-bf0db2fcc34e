<Page x:Class="SmartInvoiceManager.Views.ProductsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="ProductsView">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                     Text="إدارة المنتجات والخدمات" 
                     FontSize="24" 
                     FontWeight="Bold"
                     VerticalAlignment="Center"/>

            <Button Grid.Column="1" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" 
                                           Width="20" Height="20" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="منتج جديد" 
                             Margin="5,0,0,0" 
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- Search -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="15">
            <TextBox materialDesign:HintAssist.Hint="البحث في المنتجات..."
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
        </materialDesign:Card>

        <!-- Products List -->
        <materialDesign:Card Grid.Row="2">
            <DataGrid AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="200"/>
                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="250"/>
                    <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="100"/>
                    <DataGridTextColumn Header="الكمية المتاحة" Binding="{Binding StockQuantity}" Width="120"/>
                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
