using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartInvoiceManager.Core.Models;

public class Invoice
{
    public int Id { get; set; }

    [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
    [StringLength(50, ErrorMessage = "رقم الفاتورة يجب أن يكون أقل من 50 حرف")]
    public string InvoiceNumber { get; set; } = string.Empty;

    [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
    public DateTime InvoiceDate { get; set; } = DateTime.Now;

    public DateTime? DueDate { get; set; }

    [Required(ErrorMessage = "العميل مطلوب")]
    public int CustomerId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal SubTotal { get; set; }

    [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
    [Column(TypeName = "decimal(5,2)")]
    public decimal TaxRate { get; set; } = 15; // Default VAT rate in Saudi Arabia

    [Column(TypeName = "decimal(18,2)")]
    public decimal TaxAmount { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "قيمة الخصم يجب أن تكون أكبر من أو تساوي صفر")]
    [Column(TypeName = "decimal(18,2)")]
    public decimal DiscountAmount { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

    [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
    public string? Notes { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; }

    // Navigation Properties
    public virtual Customer Customer { get; set; } = null!;
    public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

    // Calculated Properties
    public void CalculateTotals()
    {
        SubTotal = Items.Sum(item => item.TotalPrice);
        TaxAmount = SubTotal * (TaxRate / 100);
        TotalAmount = SubTotal + TaxAmount - DiscountAmount;
    }
}

public enum InvoiceStatus
{
    Draft = 0,
    Sent = 1,
    Paid = 2,
    Overdue = 3,
    Cancelled = 4
}
