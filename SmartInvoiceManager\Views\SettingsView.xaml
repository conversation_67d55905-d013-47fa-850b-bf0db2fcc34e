<Page x:Class="SmartInvoiceManager.Views.SettingsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="SettingsView">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" 
                     Text="إعدادات النظام" 
                     FontSize="24" 
                     FontWeight="Bold"
                     Margin="0,0,0,20"/>

            <!-- Settings Content -->
            <StackPanel Grid.Row="1">
                
                <!-- Company Information -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="معلومات الشركة" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="اسم الشركة"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="0,0,10,15"/>

                            <TextBox Grid.Row="0" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="الرقم الضريبي"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="10,0,0,15"/>

                            <TextBox Grid.Row="1" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="رقم الهاتف"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="0,0,10,15"/>

                            <TextBox Grid.Row="1" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="البريد الإلكتروني"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="10,0,0,15"/>

                            <TextBox Grid.Row="2" Grid.ColumnSpan="2"
                                   materialDesign:HintAssist.Hint="العنوان"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="0,0,0,15"/>

                            <TextBox Grid.Row="3" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="الموقع الإلكتروني"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="0,0,10,15"/>

                            <TextBox Grid.Row="3" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="السجل التجاري"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Margin="10,0,0,15"/>

                            <!-- Logo Upload -->
                            <StackPanel Grid.Row="4" Grid.ColumnSpan="2" Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,0,10,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Upload" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="رفع شعار الشركة" 
                                                 Margin="5,0,0,0" 
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                                <TextBlock Text="لم يتم اختيار ملف" 
                                         VerticalAlignment="Center"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Tax Settings -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="إعدادات الضرائب" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                   materialDesign:HintAssist.Hint="نسبة الضريبة الافتراضية (%)"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Text="15"
                                   Margin="0,0,10,0"/>

                            <ComboBox Grid.Column="1"
                                    materialDesign:HintAssist.Hint="العملة"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    Margin="10,0,0,0">
                                <ComboBoxItem Content="ريال سعودي (ر.س)" IsSelected="True"/>
                                <ComboBoxItem Content="دولار أمريكي ($)"/>
                                <ComboBoxItem Content="يورو (€)"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- System Settings -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="إعدادات النظام" 
                                 FontSize="18" 
                                 FontWeight="Bold"
                                 Margin="0,0,0,15"/>

                        <StackPanel>
                            <CheckBox Content="تفعيل التحديث التلقائي" 
                                    IsChecked="True"
                                    Margin="0,0,0,10"/>
                            
                            <CheckBox Content="إرسال تقارير الأخطاء تلقائياً" 
                                    IsChecked="False"
                                    Margin="0,0,0,10"/>
                            
                            <CheckBox Content="حفظ نسخة احتياطية يومياً" 
                                    IsChecked="True"
                                    Margin="0,0,0,10"/>

                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,0,10,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Backup" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="إنشاء نسخة احتياطية" 
                                                 Margin="5,0,0,0" 
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource MaterialDesignOutlinedButton}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Restore" 
                                                               Width="16" Height="16" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="استعادة نسخة احتياطية" 
                                                 Margin="5,0,0,0" 
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Save Button -->
                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        HorizontalAlignment="Right"
                        Padding="30,10">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" 
                                               Width="20" Height="20" 
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="حفظ الإعدادات" 
                                 Margin="10,0,0,0" 
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</Page>
